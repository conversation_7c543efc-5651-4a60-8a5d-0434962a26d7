export enum WorkflowStatus {
  IDLE = 'idle',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export class WorkflowController {
  private _status: WorkflowStatus = WorkflowStatus.IDLE;
  private _abortController: AbortController | null = null;
  private _pausePromise: Promise<void> | null = null;
  private _pauseResolve: (() => void) | null = null;
  
  get status(): WorkflowStatus {
    return this._status;
  }
  
  getSignal(): AbortSignal | null {
    return this._abortController?.signal || null;
  }
  
  start(): AbortSignal {
    if (this._status === WorkflowStatus.RUNNING) {
      throw new Error('Workflow is already running');
    }
    
    this._abortController = new AbortController();
    this._status = WorkflowStatus.RUNNING;
    return this._abortController.signal;
  }
  
  stop(): void {
    if (this._status !== WorkflowStatus.RUNNING && this._status !== WorkflowStatus.PAUSED) {
      return;
    }
    
    this._status = WorkflowStatus.STOPPING;
    this._abortController?.abort('Workflow manually stopped');
    
    // If paused, resolve the pause promise to continue execution to the abort point
    if (this._pauseResolve) {
      this._pauseResolve();
      this._pausePromise = null;
      this._pauseResolve = null;
    }
  }
  
  pause(): void {
    if (this._status !== WorkflowStatus.RUNNING) {
      return;
    }
    
    this._status = WorkflowStatus.PAUSED;
    
    // Create a promise that will resolve when resume is called
    if (!this._pausePromise) {
      this._pausePromise = new Promise<void>(resolve => {
        this._pauseResolve = resolve;
      });
    }
  }
  
  resume(): void {
    if (this._status !== WorkflowStatus.PAUSED) {
      return;
    }
    
    this._status = WorkflowStatus.RUNNING;
    
    // Resolve the pause promise to continue execution
    if (this._pauseResolve) {
      this._pauseResolve();
      this._pausePromise = null;
      this._pauseResolve = null;
    }
  }
  
  async waitIfPaused(): Promise<void> {
    if (this._status === WorkflowStatus.PAUSED && this._pausePromise) {
      await this._pausePromise;
    }
  }
  
  complete(): void {
    this._status = WorkflowStatus.COMPLETED;
    this._cleanup();
  }
  
  fail(): void {
    this._status = WorkflowStatus.FAILED;
    this._cleanup();
  }
  
  private _cleanup(): void {
    this._abortController = null;
    this._pausePromise = null;
    this._pauseResolve = null;
  }
}