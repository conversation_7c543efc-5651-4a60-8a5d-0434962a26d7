class StreamingWorkflow {
  constructor(
    private readonly orchestrator: WorkflowOrchestrator,
    private readonly controller: WorkflowController = new WorkflowController()
  ) {}
  
  get status(): WorkflowStatus {
    return this.controller.status;
  }
  
  stop(): void {
    this.controller.stop();
  }
  
  pause(): void {
    this.controller.pause();
  }
  
  resume(): void {
    this.controller.resume();
  }
  
  async *processStream<T>(
    items: AsyncIterable<T>,
    startAction: string,
    batchSize: number = 10
  ): AsyncGenerator<ExecutionResult[], void, unknown> {
    const signal = this.controller.start();
    const batch: T[] = [];
    
    try {
      for await (const item of items) {
        // Check if stopped
        if (signal.aborted) {
          return;
        }
        
        // Check if paused and wait if needed
        await this.controller.waitIfPaused();
        
        batch.push(item);
        
        if (batch.length >= batchSize) {
          const results = await this.processBatch(batch, startAction);
          yield results;
          batch.length = 0;
        }
      }
      
      // Process remaining items
      if (batch.length > 0) {
        // Check again before processing final batch
        if (signal.aborted) {
          return;
        }
        
        await this.controller.waitIfPaused();
        
        const results = await this.processBatch(batch, startAction);
        yield results;
      }
      
      this.controller.complete();
    } catch (error) {
      this.controller.fail();
      throw error;
    }
  }
  
  private async processBatch(batch: any[], startAction: string): Promise<ExecutionResult[]> {
    // Process batch logic
    // ...
  }
}